"""
Backend module for image scraping and processing.

This module compiles all the image scraping functionality from multiple scripts
into a single backend service. It handles URL-based image scraping, pixel filtering,
and image collection with metadata processing.

Functions:
- scrape_images(url, min_pixels, num_images, image_types): Main function for image scraping
- scrape_images_from_url(url, image_types, min_size): Core image scraping functionality
- process_scraped_images(image_files, usage): Process and save scraped images
- save_image_metadata(images_data): Save image metadata to storage
- load_image_metadata(): Load existing image metadata
"""

import os
import json
import uuid
import base64
import requests
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple
from urllib.parse import urljoin, urlparse
from bs4 import BeautifulSoup
from PIL import Image
import io

# Default configuration
DEFAULT_IMAGE_TYPES = ['jpg', 'jpeg', 'png', 'webp', 'gif']
DEFAULT_MIN_SIZE = 100
DEFAULT_USER_AGENT = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Safari/537.3'


class ImageScraper:
    """Core image scraping engine."""
    
    def __init__(self):
        self.name = "Image Scraper"
        self.description = "Scrapes images from websites with filtering capabilities"
        self.headers = {'User-Agent': DEFAULT_USER_AGENT}
    
    def scrape_images_from_url(self, url: str, image_types: List[str], min_size: int, 
                              max_images: Optional[int] = None) -> List[Tuple[str, bytes]]:
        """
        Scrape images from a URL and return them as file-like objects.
        
        Args:
            url (str): URL to scrape images from
            image_types (List[str]): List of image file extensions to download
            min_size (int): Minimum image size in pixels (width or height)
            max_images (int, optional): Maximum number of images to download
            
        Returns:
            List[Tuple[str, bytes]]: List of (filename, image_content) tuples
        """
        try:
            # Download the webpage
            response = requests.get(url, headers=self.headers, timeout=10)
            response.raise_for_status()
            
            # Parse the HTML
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # Find all image tags
            img_tags = soup.find_all('img')
            
            print(f"Found {len(img_tags)} image tags on the page. Processing...")
            
            # Process each image
            image_files = []
            processed_count = 0
            
            for i, img in enumerate(img_tags):
                # Check if we've reached the maximum number of images
                if max_images and len(image_files) >= max_images:
                    break
                
                print(f"Processing image {i+1} of {len(img_tags)}")
                
                # Get image URL
                img_url = img.get('src')
                if not img_url:
                    continue
                
                # Make URL absolute
                img_url = urljoin(url, img_url)
                
                # Check file extension
                parsed_url = urlparse(img_url)
                ext = os.path.splitext(parsed_url.path)[1].lower().lstrip('.')
                
                # Skip if not in desired image types
                if ext not in image_types and f".{ext}" not in image_types:
                    continue
                
                try:
                    # Download image
                    img_response = requests.get(img_url, headers=self.headers, timeout=5)
                    img_response.raise_for_status()
                    
                    # Check image size using PIL
                    with Image.open(io.BytesIO(img_response.content)) as img_pil:
                        width, height = img_pil.size
                        if width < min_size or height < min_size:
                            continue  # Skip small images
                    
                    # Create a filename from URL
                    img_filename = os.path.basename(parsed_url.path)
                    if not img_filename or '.' not in img_filename:
                        img_filename = f"scraped_image_{i}.{ext if ext else 'jpg'}"
                    
                    # Add to results
                    image_files.append((img_filename, img_response.content))
                    processed_count += 1
                    
                except Exception as e:
                    print(f"Error downloading image {img_url}: {str(e)}")
                    continue
            
            print(f"Successfully processed {len(image_files)} images")
            return image_files
            
        except Exception as e:
            print(f"Error scraping URL {url}: {str(e)}")
            return []
    
    def validate_image(self, image_content: bytes, min_size: int) -> Optional[Tuple[int, int]]:
        """
        Validate image content and return dimensions if valid.
        
        Args:
            image_content (bytes): Image content to validate
            min_size (int): Minimum size requirement
            
        Returns:
            Optional[Tuple[int, int]]: (width, height) if valid, None otherwise
        """
        try:
            with Image.open(io.BytesIO(image_content)) as img:
                width, height = img.size
                if width >= min_size and height >= min_size:
                    return (width, height)
                return None
        except Exception:
            return None


class ImageProcessor:
    """Handles image processing and metadata generation."""
    
    def __init__(self):
        self.name = "Image Processor"
        self.description = "Processes scraped images and generates metadata"
    
    def process_scraped_images(self, image_files: List[Tuple[str, bytes]], usage: str = "All Products") -> List[Dict[str, Any]]:
        """
        Process scraped images and generate metadata.
        
        Args:
            image_files (List[Tuple[str, bytes]]): List of (filename, content) tuples
            usage (str): Usage category for the images
            
        Returns:
            List[Dict[str, Any]]: List of processed image metadata
        """
        processed_images = []
        
        # Ensure image directory exists
        image_dir = 'data/images'
        os.makedirs(image_dir, exist_ok=True)
        
        for i, (original_filename, file_content) in enumerate(image_files):
            try:
                print(f"Processing image {i+1} of {len(image_files)}: {original_filename}")
                
                # Generate a unique ID for the image
                image_id = str(uuid.uuid4())
                
                # Create filename with original extension
                file_ext = os.path.splitext(original_filename)[1]
                if not file_ext:
                    file_ext = '.jpg'  # Default to jpg if no extension
                filename = f"{image_id}{file_ext}"
                
                # Save image to disk
                save_path = os.path.join(image_dir, filename)
                with open(save_path, "wb") as f:
                    f.write(file_content)
                
                # Generate thumbnail and get dimensions
                img = Image.open(save_path)
                width, height = img.size
                
                # Create thumbnail
                img.thumbnail((100, 100))
                thumb_buffer = io.BytesIO()
                img.save(thumb_buffer, format="PNG")
                thumbnail_b64 = base64.b64encode(thumb_buffer.getvalue()).decode('utf-8')
                
                # Create metadata
                image_metadata = {
                    'id': image_id,
                    'filename': filename,
                    'original_filename': original_filename,
                    'name': original_filename,
                    'upload_date': datetime.now().isoformat(),
                    'categories': [],
                    'usage': usage,
                    'notes': "",
                    'width': width,
                    'height': height,
                    'thumbnail': thumbnail_b64,
                    'source': 'web_scraping'
                }
                
                processed_images.append(image_metadata)
                
            except Exception as e:
                print(f"Error processing image {original_filename}: {str(e)}")
                continue
        
        return processed_images
    
    def generate_thumbnail(self, image_path: str, size: Tuple[int, int] = (100, 100)) -> str:
        """
        Generate base64 thumbnail for an image.
        
        Args:
            image_path (str): Path to the image file
            size (Tuple[int, int]): Thumbnail size (width, height)
            
        Returns:
            str: Base64 encoded thumbnail
        """
        try:
            with Image.open(image_path) as img:
                img.thumbnail(size)
                thumb_buffer = io.BytesIO()
                img.save(thumb_buffer, format="PNG")
                return base64.b64encode(thumb_buffer.getvalue()).decode('utf-8')
        except Exception as e:
            print(f"Error generating thumbnail: {str(e)}")
            return ""


def load_image_metadata() -> Dict[str, Any]:
    """
    Load existing image metadata from storage.
    
    Returns:
        Dict[str, Any]: Image metadata dictionary
    """
    metadata_file = 'data/image_metadata.json'
    
    try:
        if os.path.exists(metadata_file):
            with open(metadata_file, 'r') as f:
                return json.load(f)
        else:
            return {'images': []}
    except (FileNotFoundError, json.JSONDecodeError):
        return {'images': []}


def save_image_metadata(image_metadata: Dict[str, Any]) -> bool:
    """
    Save image metadata to storage.
    
    Args:
        image_metadata (Dict[str, Any]): Image metadata to save
        
    Returns:
        bool: True if save successful, False otherwise
    """
    try:
        # Ensure data directory exists
        os.makedirs('data', exist_ok=True)
        
        metadata_file = 'data/image_metadata.json'
        with open(metadata_file, 'w') as f:
            json.dump(image_metadata, f, indent=2)
        
        return True
        
    except Exception as e:
        print(f"Error saving image metadata: {str(e)}")
        return False


def scrape_images(url: str, min_pixels: int = DEFAULT_MIN_SIZE, num_images: Optional[int] = None, 
                 image_types: Optional[List[str]] = None, usage: str = "All Products") -> Dict[str, Any]:
    """
    Main function that scrapes images from a URL with specified parameters.
    
    This function:
    1. Scrapes images from the provided URL
    2. Filters images by type and minimum pixel size
    3. Processes and saves images with metadata
    4. Returns scraping results
    
    Args:
        url (str): URL to scrape images from (Image Pool URL)
        min_pixels (int): Minimum image size in pixels (width or height)
        num_images (int, optional): Maximum number of images to scrape
        image_types (List[str], optional): List of image types to download
        usage (str): Usage category for the images
        
    Returns:
        Dict[str, Any]: Scraping results containing:
            - success: Boolean indicating overall success
            - total_images: Total number of images scraped
            - images_data: List of processed image metadata
            - skipped_images: Number of images skipped due to filters
            - errors: Any errors encountered
            - url: Original URL scraped
    """
    try:
        # Set default image types if not provided
        if image_types is None:
            image_types = DEFAULT_IMAGE_TYPES.copy()
        
        # Initialize scraper and processor
        scraper = ImageScraper()
        processor = ImageProcessor()
        
        # Scrape images from URL
        print(f"Starting image scraping from: {url}")
        scraped_images = scraper.scrape_images_from_url(
            url=url,
            image_types=image_types,
            min_size=min_pixels,
            max_images=num_images
        )
        
        if not scraped_images:
            return {
                "success": False,
                "total_images": 0,
                "images_data": [],
                "skipped_images": 0,
                "errors": ["No suitable images found at the provided URL"],
                "url": url
            }
        
        # Process scraped images
        print(f"Processing {len(scraped_images)} scraped images...")
        processed_images = processor.process_scraped_images(scraped_images, usage)
        
        # Load existing metadata and add new images
        existing_metadata = load_image_metadata()
        existing_metadata['images'].extend(processed_images)
        
        # Save updated metadata
        save_success = save_image_metadata(existing_metadata)
        
        if not save_success:
            return {
                "success": False,
                "total_images": len(processed_images),
                "images_data": processed_images,
                "skipped_images": 0,
                "errors": ["Failed to save image metadata"],
                "url": url
            }
        
        return {
            "success": True,
            "total_images": len(processed_images),
            "images_data": processed_images,
            "skipped_images": 0,  # This would need to be calculated during scraping
            "errors": [],
            "url": url
        }
        
    except Exception as e:
        return {
            "success": False,
            "total_images": 0,
            "images_data": [],
            "skipped_images": 0,
            "errors": [f"Failed to scrape images: {str(e)}"],
            "url": url
        }


# Utility functions
def get_image_statistics(usage_filter: Optional[str] = None) -> Dict[str, Any]:
    """
    Get statistics about scraped images.

    Args:
        usage_filter (str, optional): Filter by usage category

    Returns:
        Dict[str, Any]: Image statistics
    """
    try:
        metadata = load_image_metadata()
        images = metadata.get('images', [])

        # Apply usage filter if provided
        if usage_filter:
            images = [img for img in images if img.get('usage', '') == usage_filter]

        stats = {
            "total_images": len(images),
            "usage_categories": {},
            "image_types": {},
            "size_distribution": {
                "small": 0,    # < 500px
                "medium": 0,   # 500-1000px
                "large": 0     # > 1000px
            },
            "source_distribution": {
                "web_scraping": 0,
                "manual_upload": 0,
                "other": 0
            }
        }

        for img in images:
            # Usage categories
            usage = img.get('usage', 'Unknown')
            if usage not in stats["usage_categories"]:
                stats["usage_categories"][usage] = 0
            stats["usage_categories"][usage] += 1

            # Image types
            filename = img.get('filename', '')
            ext = os.path.splitext(filename)[1].lower().lstrip('.')
            if ext not in stats["image_types"]:
                stats["image_types"][ext] = 0
            stats["image_types"][ext] += 1

            # Size distribution
            width = img.get('width', 0)
            height = img.get('height', 0)
            max_dimension = max(width, height)

            if max_dimension < 500:
                stats["size_distribution"]["small"] += 1
            elif max_dimension < 1000:
                stats["size_distribution"]["medium"] += 1
            else:
                stats["size_distribution"]["large"] += 1

            # Source distribution
            source = img.get('source', 'manual_upload')
            if source in stats["source_distribution"]:
                stats["source_distribution"][source] += 1
            else:
                stats["source_distribution"]["other"] += 1

        return stats

    except Exception as e:
        return {
            "total_images": 0,
            "usage_categories": {},
            "image_types": {},
            "size_distribution": {"small": 0, "medium": 0, "large": 0},
            "source_distribution": {"web_scraping": 0, "manual_upload": 0, "other": 0},
            "error": str(e)
        }


def get_images_by_usage(usage: str) -> List[Dict[str, Any]]:
    """
    Get all images for a specific usage category.

    Args:
        usage (str): Usage category to filter by

    Returns:
        List[Dict[str, Any]]: List of image metadata for the usage category
    """
    try:
        metadata = load_image_metadata()
        images = metadata.get('images', [])

        return [img for img in images if img.get('usage', '') == usage]

    except Exception:
        return []


def delete_images(image_ids: List[str]) -> bool:
    """
    Delete images by their IDs.

    Args:
        image_ids (List[str]): List of image IDs to delete

    Returns:
        bool: True if deletion successful, False otherwise
    """
    try:
        metadata = load_image_metadata()
        images = metadata.get('images', [])

        # Remove images with matching IDs
        updated_images = [img for img in images if img.get('id', '') not in image_ids]

        # Delete image files from disk
        image_dir = 'data/images'
        for img in images:
            if img.get('id', '') in image_ids:
                filename = img.get('filename', '')
                if filename:
                    file_path = os.path.join(image_dir, filename)
                    try:
                        if os.path.exists(file_path):
                            os.remove(file_path)
                    except Exception as e:
                        print(f"Error deleting file {file_path}: {str(e)}")

        # Save updated metadata
        metadata['images'] = updated_images
        return save_image_metadata(metadata)

    except Exception as e:
        print(f"Error deleting images: {str(e)}")
        return False


def validate_url(url: str) -> bool:
    """
    Validate if a URL is accessible for scraping.

    Args:
        url (str): URL to validate

    Returns:
        bool: True if URL is accessible, False otherwise
    """
    try:
        headers = {'User-Agent': DEFAULT_USER_AGENT}
        response = requests.head(url, headers=headers, timeout=5)
        return response.status_code == 200
    except Exception:
        return False


def get_supported_image_types() -> List[str]:
    """
    Get list of supported image types.

    Returns:
        List[str]: List of supported image file extensions
    """
    return DEFAULT_IMAGE_TYPES.copy()


def estimate_scraping_time(url: str) -> Dict[str, Any]:
    """
    Estimate scraping time and image count for a URL.

    Args:
        url (str): URL to analyze

    Returns:
        Dict[str, Any]: Estimation results
    """
    try:
        headers = {'User-Agent': DEFAULT_USER_AGENT}
        response = requests.get(url, headers=headers, timeout=10)
        response.raise_for_status()

        soup = BeautifulSoup(response.text, 'html.parser')
        img_tags = soup.find_all('img')

        estimated_time = len(img_tags) * 2  # Rough estimate: 2 seconds per image

        return {
            "success": True,
            "estimated_images": len(img_tags),
            "estimated_time_seconds": estimated_time,
            "estimated_time_minutes": round(estimated_time / 60, 1),
            "url": url
        }

    except Exception as e:
        return {
            "success": False,
            "estimated_images": 0,
            "estimated_time_seconds": 0,
            "estimated_time_minutes": 0,
            "error": str(e),
            "url": url
        }


def filter_images_by_size(images: List[Dict[str, Any]], min_width: int = 0, min_height: int = 0,
                         max_width: int = float('inf'), max_height: int = float('inf')) -> List[Dict[str, Any]]:
    """
    Filter images by size criteria.

    Args:
        images (List[Dict[str, Any]]): List of image metadata
        min_width (int): Minimum width in pixels
        min_height (int): Minimum height in pixels
        max_width (int): Maximum width in pixels
        max_height (int): Maximum height in pixels

    Returns:
        List[Dict[str, Any]]: Filtered list of images
    """
    filtered_images = []

    for img in images:
        width = img.get('width', 0)
        height = img.get('height', 0)

        if (min_width <= width <= max_width and
            min_height <= height <= max_height):
            filtered_images.append(img)

    return filtered_images


def get_image_by_id(image_id: str) -> Optional[Dict[str, Any]]:
    """
    Get image metadata by ID.

    Args:
        image_id (str): Image ID to search for

    Returns:
        Optional[Dict[str, Any]]: Image metadata if found, None otherwise
    """
    try:
        metadata = load_image_metadata()
        images = metadata.get('images', [])

        for img in images:
            if img.get('id', '') == image_id:
                return img

        return None

    except Exception:
        return None


def update_image_metadata(image_id: str, updates: Dict[str, Any]) -> bool:
    """
    Update metadata for a specific image.

    Args:
        image_id (str): Image ID to update
        updates (Dict[str, Any]): Fields to update

    Returns:
        bool: True if update successful, False otherwise
    """
    try:
        metadata = load_image_metadata()
        images = metadata.get('images', [])

        for img in images:
            if img.get('id', '') == image_id:
                img.update(updates)
                return save_image_metadata(metadata)

        return False  # Image not found

    except Exception as e:
        print(f"Error updating image metadata: {str(e)}")
        return False


# Example usage and testing function
def test_image_scraping(test_url: str = "https://www.analyticsvidhya.com/") -> None:
    """
    Test function to demonstrate image scraping.

    Args:
        test_url (str): URL to test with (defaults to Analytics Vidhya)
    """
    print(f"Testing image scraping for: {test_url}")
    print("-" * 50)

    # Test parameters
    min_pixels = 200
    num_images = 5
    image_types = ['jpg', 'jpeg', 'png']
    usage = "Analytics Vidhya"

    # Validate URL first
    if not validate_url(test_url):
        print(f"❌ URL {test_url} is not accessible")
        return

    print(f"✅ URL {test_url} is accessible")

    # Estimate scraping time
    estimation = estimate_scraping_time(test_url)
    if estimation["success"]:
        print(f"📊 Estimation: {estimation['estimated_images']} images found")
        print(f"⏱️ Estimated time: {estimation['estimated_time_minutes']} minutes")
    else:
        print(f"❌ Failed to estimate: {estimation.get('error', 'Unknown error')}")

    # Scrape images
    print(f"\n🔍 Starting image scraping...")
    results = scrape_images(
        url=test_url,
        min_pixels=min_pixels,
        num_images=num_images,
        image_types=image_types,
        usage=usage
    )

    # Display results
    print("\n📋 Image Scraping Results:")
    print(f"Success: {results['success']}")
    print(f"Total Images Scraped: {results['total_images']}")
    print(f"Skipped Images: {results['skipped_images']}")

    if results['errors']:
        print(f"❌ Errors: {results['errors']}")

    # Display image details
    if results['images_data']:
        print(f"\n🖼️ Scraped Images:")
        for i, img in enumerate(results['images_data'][:3], 1):  # Show first 3
            print(f"{i}. {img['name']} ({img['width']}x{img['height']})")

    # Display statistics
    stats = get_image_statistics(usage)
    print(f"\n📈 Image Statistics for {usage}:")
    print(f"Total Images: {stats['total_images']}")
    print(f"Image Types: {stats['image_types']}")
    print(f"Size Distribution: {stats['size_distribution']}")

    # Test utility functions
    print(f"\n🔧 Testing Utility Functions:")
    print(f"Supported Types: {get_supported_image_types()}")

    if results['images_data']:
        # Test filtering
        large_images = filter_images_by_size(results['images_data'], min_width=500, min_height=500)
        print(f"Large Images (>500px): {len(large_images)}")


if __name__ == "__main__":
    # Test the module
    test_image_scraping()

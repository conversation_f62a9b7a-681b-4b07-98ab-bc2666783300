"""
Backend module for brand guidelines processing.

This module compiles all the brand-related functionality from multiple scripts
into a single backend service. It handles brand analysis, color extraction,
typography detection, CTA theme analysis, and brand archetype analysis.

Functions:
- get_brand_guidelines(org_url): Main function that takes org URL and returns brand guidelines
- scrape_website_content(url): Scrapes HTML and CSS content from organization website
- analyze_brand_guidelines(url, html, css): Analyzes website content to extract brand details
- analyze_brand_archetypes(brand_data): Analyzes brand archetypes with scoring
- save_brand_data(guidelines): Saves brand guidelines to storage
- load_brand_data(): Loads existing brand guidelines
"""

import os
import json
import re
import requests
from bs4 import BeautifulSoup
from typing import Dict, List, Any, Optional, Tuple
from dotenv import load_dotenv
from openai import OpenAI

# Load environment variables
load_dotenv()


class WebScrapingTool:
    """Tool for scraping web content and extracting HTML/CSS from brand websites."""
    
    def __init__(self):
        self.name = "Web Scraper"
        self.description = "Scrapes HTML and CSS content from websites for brand analysis"
    
    def extract_html_and_css(self, url: str) -> <PERSON>ple[BeautifulSoup, str]:
        """
        Extract HTML content and CSS from a website.
        
        Args:
            url (str): The URL to scrape
            
        Returns:
            Tuple[BeautifulSoup, str]: Parsed HTML soup and combined CSS content
        """
        try:
            # Send request with headers to mimic a browser
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            }
            response = requests.get(url, headers=headers, timeout=10)
            response.raise_for_status()

            # Parse content
            soup = BeautifulSoup(response.text, 'html.parser')

            # Extract inline CSS
            inline_css = ""
            for style in soup.find_all('style'):
                inline_css += style.string if style.string else ""

            # Extract linked CSS files
            linked_css = ""
            for link in soup.find_all('link', rel='stylesheet'):
                css_url = link.get('href')
                if css_url:
                    # Handle relative URLs
                    if css_url.startswith('/'):
                        base_url = '/'.join(url.split('/')[:3])  # http(s)://domain.com
                        css_url = base_url + css_url
                    elif not css_url.startswith(('http://', 'https://')):
                        css_url = url.rstrip('/') + '/' + css_url

                    try:
                        css_response = requests.get(css_url, headers=headers, timeout=5)
                        if css_response.status_code == 200:
                            linked_css += css_response.text
                    except:
                        pass  # Skip if CSS file can't be fetched

            # Extract inline styles from elements for gradient detection
            inline_styles = ""
            for element in soup.find_all(style=True):
                inline_styles += element.get('style', '') + "\n"

            # Combine all CSS
            all_css = inline_css + linked_css + inline_styles

            return soup, all_css

        except Exception as e:
            raise Exception(f"Failed to extract website content: {str(e)}")


class BrandAnalyzerTool:
    """Tool for analyzing brand guidelines from website content."""
    
    def __init__(self):
        self.name = "Brand Analyzer"
        self.description = "Analyzes website HTML and CSS to extract brand guidelines and visual identity elements"
        self.client = OpenAI(api_key=os.getenv("OPENAI_API_KEY"))
    
    def analyze_brand_content(self, url: str, soup: BeautifulSoup, css: str) -> Dict[str, Any]:
        """
        Analyze website content to extract brand guidelines.
        
        Args:
            url (str): Website URL
            soup (BeautifulSoup): Parsed HTML content
            css (str): Combined CSS content
            
        Returns:
            Dict[str, Any]: Dictionary containing brand guidelines
        """
        try:
            # Get text content for mission/vision analysis
            text_content = soup.get_text()

            # Prepare prompt with HTML, CSS, and extracted text
            html_sample = str(soup.prettify())[:3000]  # First 3000 chars of HTML
            css_sample = css[:3000]  # First 3000 chars of CSS

            # Pre-analyze CSS for gradient patterns
            has_gradient, gradient_colors, gradient_direction = self._detect_gradients(css)

            response = self.client.chat.completions.create(
                model="gpt-4o-mini-2024-07-18",
                messages=[
                    {
                        "role": "system",
                        "content": """You are a brand identity expert who analyzes websites to extract branding guidelines.
                        Look for patterns in colors, typography, button styles, and company information.

                        Analyze the provided HTML and CSS to extract:
                        1. Color scheme(as hex codes):
                            -Primary Color: The core color that represents your brand's identity and is used most prominently.
                            -Secondary Color: A complementary color that adds depth and variety to your visual palette.
                            -Accent Color: A bold, eye-catching color used sparingly to highlight important elements or actions.
                            -Neutral Color: Subtle tones like gray, beige, or white that provide balance and support the main colors.
                            -Background Color: The base color used behind content to create contrast and visual clarity.
                            -Text Color: A color chosen for legibility and contrast to ensure your message is readable on all backgrounds.
                        2. Gradient colors: look for linear-gradient, radial-gradient, etc. in CSS
                        3. Button/CTA styling: type (Button/Link), size (Small/Medium/Large), style (Rounded/Square/Pill), border radius
                        4. Typography: font family, size, weight
                        5. Brand identity: mission, vision, personality traits, tone of voice (if available)

                        For colors, look for the most prominent and consistently used colors in headers, buttons, backgrounds.
                        For missing values, provide reasonable defaults based on common web design patterns.

                        For brand personality, you MUST analyze ALL of these 12 Brand Archetypes and provide a score (0-10) and detailed reasoning for each one. Then select exactly ONE archetype that best matches the brand as the primary brand personality. Definition of all archetypes are given below:
                        1. Creator – Innovates to build original, lasting products or experiences that express their vision.
                        2. Sage – Seeks truth and wisdom to enlighten others through knowledge and insight.
                        3. Caregiver – Protects and nurtures others with compassion and selflessness.
                        4. Innocent – Spreads joy and optimism by living simply and doing what's right.
                        5. Jester – Brings happiness through humor, fun, and lightheartedness.
                        6. Magician – Transforms reality to create awe-inspiring, dream-like experiences.
                        7. Ruler – Leads with authority and order to achieve control, success, and stability.
                        8. Hero – Strives to overcome challenges and inspire through courage and determination.
                        9. Everyman – Relatable and grounded, values connection and belonging for all.
                        10. Rebel – Challenges norms to spark change and revolution with bold independence.
                        11. Explorer – Embarks on adventures to discover new experiences and personal freedom.
                        12. Lover – Pursues deep emotional and physical connections through passion and desire.

                        Do not use any other personality descriptions or multiple archetypes for the primary brand personality.
                        """
                    },
                    {
                        "role": "user",
                        "content": f"""Analyze this website with URL: {url}

                        HTML Sample:
                        {html_sample}

                        CSS Sample:
                        {css_sample}

                        Text content sample (for mission/vision analysis):
                        {text_content[:3000]}

                        Extract the brand guidelines and return as JSON with these exact keys:
                        {{
                            "primary_color": "#hex",
                            "secondary_color": "#hex",
                            "accent_color": "#hex",
                            "neutral_color": "#hex",
                            "background_color": "#hex",
                            "text_color": "#hex",
                            "has_gradient": true/false,
                            "gradient_colors": "description of gradient colors or null",
                            "gradient_direction": "direction of gradient or null",
                            "cta_type": "Button or Link",
                            "cta_size": "Small/Medium/Large",
                            "button_style": "Rounded/Square/Pill",
                            "border_radius": "size with unit",
                            "font": "Font Family",
                            "font_size": "size with unit",
                            "font_weight": "Normal/Bold/etc",
                            "mission": "Mission statement if found",
                            "vision": "Vision statement if found",
                            "brand_personality": "One of the following brand archetypes: The Innocent, Everyman, Hero, Outlaw, Explorer, Creator, Ruler, Magician, Lover, Caregiver, Jester, or Sage",
                            "brand_personality_reasoning": "Explanation of why this brand archetype was selected, based on website content, visuals, messaging, tone, and overall brand presentation",
                            "tone_of_voice": "3-4 brand tone of voice Separated by Commas",
                            "archetype_scores": {{
                                "Creator": {{"score": 0-10, "reasoning": "Detailed reasoning"}},
                                "Sage": {{"score": 0-10, "reasoning": "Detailed reasoning"}},
                                "Caregiver": {{"score": 0-10, "reasoning": "Detailed reasoning"}},
                                "Innocent": {{"score": 0-10, "reasoning": "Detailed reasoning"}},
                                "Jester": {{"score": 0-10, "reasoning": "Detailed reasoning"}},
                                "Magician": {{"score": 0-10, "reasoning": "Detailed reasoning"}},
                                "Ruler": {{"score": 0-10, "reasoning": "Detailed reasoning"}},
                                "Hero": {{"score": 0-10, "reasoning": "Detailed reasoning"}},
                                "Everyman": {{"score": 0-10, "reasoning": "Detailed reasoning"}},
                                "Rebel": {{"score": 0-10, "reasoning": "Detailed reasoning"}},
                                "Explorer": {{"score": 0-10, "reasoning": "Detailed reasoning"}},
                                "Lover": {{"score": 0-10, "reasoning": "Detailed reasoning"}}
                            }}
                        }}

                        If you can't determine specific values, use sensible defaults but note them as 'default'.

                        For the gradient analysis, I've already detected: has_gradient={has_gradient}, gradient_colors='{gradient_colors}', gradient_direction='{gradient_direction}'. Use this information in your analysis."""
                    }
                ],
                temperature=0.3,
                max_tokens=1024,
                response_format={"type": "json_object"}
            )

            response_content = response.choices[0].message.content
            parsed_response = json.loads(response_content)

            # Assemble return data with brand guidelines
            return_data = {
                "primary_color": parsed_response.get("primary_color", "#default"),
                "secondary_color": parsed_response.get("secondary_color", "#default"),
                "accent_color": parsed_response.get("accent_color", "#default"),
                "neutral_color": parsed_response.get("neutral_color", "#FFFFFF"),
                "background_color": parsed_response.get("background_color", "#F5F5F5"),
                "text_color": parsed_response.get("text_color", "#212121"),
                "cta_type": parsed_response.get("cta_type", "Button"),
                "cta_size": parsed_response.get("cta_size", "Medium"),
                "button_style": parsed_response.get("button_style", "Rounded"),
                "border_radius": parsed_response.get("border_radius", "8px"),
                "font": parsed_response.get("font", "Arial"),
                "font_size": parsed_response.get("font_size", "16px"),
                "font_weight": parsed_response.get("font_weight", "Normal"),
                "mission": parsed_response.get("mission", ""),
                "vision": parsed_response.get("vision", ""),
                "brand_personality": parsed_response.get("brand_personality", ""),
                "brand_personality_reasoning": parsed_response.get("brand_personality_reasoning", ""),
                "tone_of_voice": parsed_response.get("tone_of_voice", ""),
                "archetype_scores": parsed_response.get("archetype_scores", {}),
                "organization_url": url
            }

            # Add gradient information if found
            if has_gradient and gradient_colors:
                return_data["has_gradient"] = True
                return_data["gradient_colors"] = gradient_colors
                return_data["gradient_direction"] = gradient_direction

            return return_data

        except Exception as e:
            # Return default guidelines if analysis fails
            return self._get_default_guidelines(url, str(e))
    
    def _detect_gradients(self, css: str) -> Tuple[bool, Optional[str], Optional[str]]:
        """
        Detect gradient patterns in CSS.
        
        Args:
            css (str): CSS content to analyze
            
        Returns:
            Tuple[bool, Optional[str], Optional[str]]: has_gradient, gradient_colors, gradient_direction
        """
        gradient_patterns = [
            r'linear-gradient\s*\([^)]+\)',
            r'radial-gradient\s*\([^)]+\)',
            r'conic-gradient\s*\([^)]+\)',
            r'repeating-linear-gradient\s*\([^)]+\)',
            r'repeating-radial-gradient\s*\([^)]+\)'
        ]

        all_gradients = []
        for pattern in gradient_patterns:
            gradient_matches = re.findall(pattern, css)
            all_gradients.extend(gradient_matches)

        # Sort by length to find the most complex/detailed gradient
        all_gradients.sort(key=len, reverse=True)

        if all_gradients:
            gradient_colors = all_gradients[0]  # Get the most complex match

            # Try to extract direction
            direction_match = re.search(r'(?:to\s+(?:top|bottom|left|right|top\s+left|top\s+right|bottom\s+left|bottom\s+right)|\d+deg)', gradient_colors)
            gradient_direction = direction_match.group(0) if direction_match else "not specified"

            return True, gradient_colors, gradient_direction

        return False, None, None
    
    def _get_default_guidelines(self, url: str, error: str) -> Dict[str, Any]:
        """
        Return default brand guidelines when analysis fails.
        
        Args:
            url (str): Website URL
            error (str): Error message
            
        Returns:
            Dict[str, Any]: Default brand guidelines
        """
        return {
            "primary_color": "#default",
            "secondary_color": "#default",
            "accent_color": "#default",
            "neutral_color": "#default",
            "background_color": "#default",
            "text_color": "#default",
            "has_gradient": False,
            "gradient_colors": None,
            "gradient_direction": None,
            "cta_type": "Button",
            "cta_size": "Medium",
            "button_style": "Rounded",
            "border_radius": "4px",
            "font": "Default system font",
            "font_size": "16px",
            "font_weight": "Normal",
            "mission": None,
            "vision": None,
            "brand_personality": None,
            "brand_personality_reasoning": None,
            "tone_of_voice": None,
            "archetype_scores": {},
            "organization_url": url,
            "error": error
        }


def scrape_website_content(url: str) -> Tuple[BeautifulSoup, str]:
    """
    Scrape HTML and CSS content from organization website.

    Args:
        url (str): Organization website URL

    Returns:
        Tuple[BeautifulSoup, str]: Parsed HTML soup and combined CSS content
    """
    scraper = WebScrapingTool()
    return scraper.extract_html_and_css(url)


def analyze_brand_guidelines(url: str, soup: BeautifulSoup, css: str) -> Dict[str, Any]:
    """
    Analyze website content to extract brand guidelines.

    Args:
        url (str): Website URL
        soup (BeautifulSoup): Parsed HTML content
        css (str): Combined CSS content

    Returns:
        Dict[str, Any]: Brand guidelines including colors, typography, CTA theme, etc.
    """
    analyzer = BrandAnalyzerTool()
    return analyzer.analyze_brand_content(url, soup, css)


def get_brand_guidelines(org_url: str) -> Dict[str, Any]:
    """
    Main function that takes organization URL and returns complete brand guidelines.

    This function:
    1. Scrapes HTML and CSS content from the organization website
    2. Analyzes the content using AI to extract brand guidelines
    3. Determines brand archetype and personality
    4. Returns structured brand guidelines data

    Args:
        org_url (str): Organization website URL

    Returns:
        Dict[str, Any]: Complete brand guidelines containing:
            - Color scheme: primary_color, secondary_color, accent_color, neutral_color, background_color, text_color
            - Gradient information: has_gradient, gradient_colors, gradient_direction
            - CTA theme: cta_type, cta_size, button_style, border_radius
            - Typography: font, font_size, font_weight
            - Brand identity: mission, vision, brand_personality, tone_of_voice
            - Archetype analysis: archetype_scores with detailed reasoning
            - organization_url: Original URL provided
    """
    try:
        # Step 1: Scrape website HTML and CSS content
        soup, css = scrape_website_content(org_url)

        # Step 2: Analyze brand guidelines
        brand_data = analyze_brand_guidelines(org_url, soup, css)

        # Step 3: Add additional processing for button color type
        brand_data = _determine_button_color_type(brand_data)

        return brand_data

    except Exception as e:
        # Return default guidelines with error information
        analyzer = BrandAnalyzerTool()
        return analyzer._get_default_guidelines(org_url, str(e))


def _determine_button_color_type(brand_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Determine button color type based on gradient and color analysis.

    Args:
        brand_data (Dict[str, Any]): Brand guidelines data

    Returns:
        Dict[str, Any]: Brand data with button_color_type added
    """
    if brand_data.get("has_gradient", False):
        brand_data["button_color_type"] = "Gradient"
    elif brand_data.get("primary_color") != "#default":
        brand_data["button_color_type"] = "Primary Color"
    elif brand_data.get("secondary_color") != "#default":
        brand_data["button_color_type"] = "Secondary Color"
    elif brand_data.get("accent_color") != "#default":
        brand_data["button_color_type"] = "Accent Color"
    else:
        brand_data["button_color_type"] = "Primary Color"

    return brand_data


# Data management functions
def save_brand_guidelines(guidelines: Dict[str, Any], organization_url: Optional[str] = None) -> bool:
    """
    Save brand guidelines to the data storage file.

    Args:
        guidelines (Dict[str, Any]): Brand guidelines to save
        organization_url (str, optional): URL of the organization

    Returns:
        bool: True if save successful, False otherwise
    """
    if not guidelines:
        return False

    if not organization_url:
        organization_url = guidelines.get('organization_url')
        if not organization_url:
            return False

    try:
        # Ensure guidelines has organization_url
        guidelines['organization_url'] = organization_url

        # Load existing guidelines
        all_guidelines = load_brand_guidelines()

        # Update guidelines for this organization
        all_guidelines[organization_url] = guidelines

        # Ensure data directory exists
        os.makedirs('data', exist_ok=True)

        # Save back to file
        guidelines_path = os.path.join('data', 'brand_guidelines.json')
        with open(guidelines_path, 'w') as f:
            json.dump(all_guidelines, f, indent=2)

        return True

    except Exception as e:
        print(f"Error saving brand guidelines: {str(e)}")
        return False


def load_brand_guidelines(organization_url: Optional[str] = None) -> Dict[str, Any]:
    """
    Load brand guidelines for a specific organization or all organizations.

    Args:
        organization_url (str, optional): URL of the organization to load.
            If None, returns all organizations as a dictionary.

    Returns:
        Dict[str, Any]: Brand guidelines for a specific organization or all organizations
    """
    guidelines_path = os.path.join('data', 'brand_guidelines.json')

    # Default brand guidelines
    default_guidelines = {
        "primary_color": "#default",
        "secondary_color": "#default",
        "accent_color": "#default",
        "neutral_color": "#default",
        "background_color": "#default",
        "text_color": "#default",
        "cta_type": "Button",
        "cta_size": "Medium",
        "button_style": "Rounded",
        "border_radius": "4px",
        "font": "Default system font",
        "font_size": "16px",
        "font_weight": "Normal",
        "organization_url": ""
    }

    try:
        if os.path.exists(guidelines_path):
            with open(guidelines_path, 'r') as f:
                all_guidelines = json.load(f)

                if organization_url:
                    return all_guidelines.get(organization_url, default_guidelines)
                else:
                    return all_guidelines
        else:
            if organization_url:
                return default_guidelines
            else:
                return {'default_organization': default_guidelines}
    except (FileNotFoundError, json.JSONDecodeError):
        if organization_url:
            return default_guidelines
        else:
            return {'default_organization': default_guidelines}


def get_all_brand_guidelines() -> Dict[str, Dict[str, Any]]:
    """
    Get all brand guidelines for all organizations.

    Returns:
        Dict[str, Dict[str, Any]]: Dictionary mapping organization URLs to their brand guidelines
    """
    return load_brand_guidelines()


def update_brand_guidelines(organization_url: str, updates: Dict[str, Any]) -> bool:
    """
    Update specific fields in brand guidelines for an organization.

    Args:
        organization_url (str): URL of the organization
        updates (Dict[str, Any]): Fields to update

    Returns:
        bool: True if update successful, False otherwise
    """
    try:
        # Load existing guidelines
        existing_guidelines = load_brand_guidelines(organization_url)

        # Update with new values
        existing_guidelines.update(updates)
        existing_guidelines['organization_url'] = organization_url

        # Save updated guidelines
        return save_brand_guidelines(existing_guidelines, organization_url)

    except Exception as e:
        print(f"Error updating brand guidelines: {str(e)}")
        return False


def delete_brand_guidelines(organization_url: str) -> bool:
    """
    Delete brand guidelines for a specific organization.

    Args:
        organization_url (str): URL of the organization

    Returns:
        bool: True if deletion successful, False otherwise
    """
    try:
        all_guidelines = load_brand_guidelines()

        if organization_url in all_guidelines:
            del all_guidelines[organization_url]

            # Save updated guidelines
            guidelines_path = os.path.join('data', 'brand_guidelines.json')
            with open(guidelines_path, 'w') as f:
                json.dump(all_guidelines, f, indent=2)

            return True
        else:
            return False  # Organization not found

    except Exception as e:
        print(f"Error deleting brand guidelines: {str(e)}")
        return False


# Example usage and testing function
def test_brand_analysis(test_url: str = "https://www.analyticsvidhya.com/") -> None:
    """
    Test function to demonstrate brand guidelines extraction.

    Args:
        test_url (str): URL to test with (defaults to Analytics Vidhya)
    """
    print(f"Testing brand analysis for: {test_url}")
    print("-" * 50)

    # Get brand guidelines
    brand_data = get_brand_guidelines(test_url)

    # Display results
    print("Brand Guidelines Analysis Results:")
    print(f"Primary Color: {brand_data.get('primary_color', 'N/A')}")
    print(f"Secondary Color: {brand_data.get('secondary_color', 'N/A')}")
    print(f"Accent Color: {brand_data.get('accent_color', 'N/A')}")
    print(f"Has Gradient: {brand_data.get('has_gradient', 'N/A')}")
    print(f"CTA Type: {brand_data.get('cta_type', 'N/A')}")
    print(f"Button Style: {brand_data.get('button_style', 'N/A')}")
    print(f"Font: {brand_data.get('font', 'N/A')}")
    print(f"Brand Personality: {brand_data.get('brand_personality', 'N/A')}")
    print(f"Tone of Voice: {brand_data.get('tone_of_voice', 'N/A')}")

    # Save the data
    if save_brand_guidelines(brand_data, test_url):
        print("\n✅ Brand guidelines saved successfully!")
    else:
        print("\n❌ Failed to save brand guidelines!")


if __name__ == "__main__":
    # Test the module
    test_brand_analysis()
